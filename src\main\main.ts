// ================= BOOTSTRAP ERROR HANDLER =================
// 在所有模块加载之前，立即设置一个最基础的错误处理器。
// 这可以捕获在初始化阶段发生的、可能导致应用静默退出的致命错误。
import { appendFileSync, existsSync, mkdirSync }from 'fs';
import { join } from 'path';

const rootDir = join(__dirname, '..', '..'); // 项目根目录
const logDir = join(rootDir, 'logs');
const crashLogPath = join(rootDir, 'crash.log');

// 确保日志目录存在
if (!existsSync(logDir)) {
  try {
    mkdirSync(logDir, { recursive: true });
  } catch (e) {
    // 如果创建目录失败，至少尝试在根目录记录崩溃日志
  }
}


const emergencyLog = (message: string): void => {
  try {
    const timestamp = new Date().toISOString();
    appendFileSync(crashLogPath, `${timestamp} - ${message}\n`, 'utf8');
  } catch (e) {
    // 如果连日志都写不了，那就没办法了
  }
};

process.on('uncaughtException', (error: Error) => {
  emergencyLog(`UNCAUGHT EXCEPTION:\n${error.stack || error.message}`);
  process.exit(1); // 捕获到未处理异常后，强制退出
});

process.on('unhandledRejection', (reason: unknown) => {
  const error = reason instanceof Error ? reason : new Error(String(reason));
  emergencyLog(`UNHANDLED REJECTION:\n${error.stack || error.message}`);
  process.exit(1); // 捕获到未处理的Promise拒绝后，强制退出
});

emergencyLog('Bootstrap error handler initialized.');
// =========================================================

import { app, BrowserWindow, shell, Menu, Tray, nativeImage } from 'electron';
import { registerIPCHandlers } from './ipc-handlers';
import { initializeAppDirectories } from './config';
import { configManager } from './config-manager';
import { databaseManager } from './database-manager';
import { backgroundCacheManager } from './background-cache-manager';
import { createMainLogger, LogCategory, createPerformanceLogger, createErrorLogger, LogLevel } from '../shared/logger';
import { mainI18n } from './i18n';
import type { SupportedLanguage } from '../i18n';


// 强制Windows终端使用UTF-8编码
if (process.platform === 'win32') {
  process.env.CHCP = '65001'; // 设置代码页为UTF-8

  // 设置进程编码
  if (process.stdout.setDefaultEncoding) {
    process.stdout.setDefaultEncoding('utf8');
  }
  if (process.stderr.setDefaultEncoding) {
    process.stderr.setDefaultEncoding('utf8');
  }

  // 设置环境变量强制UTF-8编码
  process.env.PYTHONIOENCODING = 'utf-8';
  process.env.LANG = 'zh_CN.UTF-8';
  process.env.LC_ALL = 'zh_CN.UTF-8';

  // 尝试设置Windows控制台代码页为UTF-8
  try {
    const { execSync } = require('child_process');
    execSync('chcp 65001', { stdio: 'ignore' });
  } catch {
    // 忽略错误，继续执行
  }
}

// 初始化日志系统
const logger = createMainLogger({
  logDir: join(process.env.APPDATA ?? process.env.HOME ?? process.cwd(), 'QuickStartAPP', 'logs'),
  level: 2, // INFO
  enableFile: true,
  enableConsole: true,
});

const perfLogger = createPerformanceLogger(logger);
const errorLogger = createErrorLogger(logger);

// 全局错误处理
process.on('uncaughtException', (error) => {
  errorLogger.logError(error, 'main.ts', LogCategory.APP).catch(console.error);
  // 使用i18n日志记录器记录系统错误
  logger.logSystemError('main.ts', error).catch(console.error);
});

process.on('unhandledRejection', (reason) => {
  const error = reason instanceof Error ? reason : new Error(String(reason));
  errorLogger.logError(error, 'main.ts', LogCategory.APP).catch(console.error);
  // 使用i18n记录未处理的Promise拒绝
  logger.logI18n(
    LogLevel.ERROR,
    'logs.system.error.unhandled',
    'Unhandled promise rejection: {{reason}}',
    LogCategory.APP,
    'main.ts',
    { reason: String(reason) }
  ).catch(console.error);
});

// 记录主进程启动
logger.logAppStartupSuccess('main.ts', app.getVersion()).catch(console.error);

// 全局变量
let mainWindow: BrowserWindow | null = null;
let tray: Tray | null = null;

// 开发环境检测 - 更精确的检测逻辑
const isDev = process.env.NODE_ENV === 'development';
const RENDERER_URL = isDev ? 'http://localhost:3000' : `file://${join(__dirname, '../renderer/index.html')}`;

// 环境信息将在app ready后输出

/**
 * 创建主窗口
 */
async function createMainWindow(): Promise<void> {
  perfLogger.start('createMainWindow');
  await logger.logI18n(LogLevel.INFO, 'logs.app.window.creating', 'Creating main window...', LogCategory.APP, 'main.ts');

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false, // 初始隐藏，等待ready-to-show事件
    titleBarStyle: 'default',
    icon: join(__dirname, '../../assets/icon.png'),
    webPreferences: {
      // 安全配置 - 开发环境下放宽限制以支持热重载
      contextIsolation: true,        // 启用上下文隔离
      sandbox: !isDev,               // 开发环境禁用沙箱以支持热重载
      nodeIntegration: false,        // 禁用Node.js集成
      webSecurity: !isDev,           // 开发环境禁用Web安全以支持热重载
      allowRunningInsecureContent: isDev,  // 开发环境允许不安全内容
      experimentalFeatures: false,

      // 预加载脚本
      preload: join(__dirname, '../preload/preload.js'),

      // 其他安全设置
      backgroundThrottling: false
    }
  });

  // 加载渲染进程
  await logger.logI18n(LogLevel.INFO, 'logs.app.window.loading_url', 'Loading renderer URL: {{url}}', LogCategory.APP, 'main.ts', { url: RENDERER_URL });
  mainWindow.loadURL(RENDERER_URL);

  // 窗口事件处理
  mainWindow.once('ready-to-show', async () => {
    if (mainWindow) {
      mainWindow.show();
      await logger.logI18n(LogLevel.INFO, 'logs.app.window.shown', 'Main window shown', LogCategory.APP, 'main.ts');
      await perfLogger.end('createMainWindow', 'main.ts');

      // 开发环境下打开开发者工具
      if (isDev) {
        mainWindow.webContents.openDevTools();
      }
    }
  });

  // 添加页面加载事件监听
  mainWindow.webContents.on('did-finish-load', async () => {
    await logger.logI18n(LogLevel.INFO, 'logs.app.window.renderer_loaded', 'Renderer process finished loading', LogCategory.APP, 'main.ts');
  });

  mainWindow.webContents.on('did-fail-load', async (event, errorCode, errorDescription) => {
    const error = new Error(`Renderer load failed: ${errorDescription} (${errorCode})`);
    await errorLogger.logError(error, 'main.ts', LogCategory.APP);
  });

  // 监听渲染进程的控制台消息
  mainWindow.webContents.on('console-message', async (event, level, message, _line, _sourceId) => {
    await logger.debug(`Renderer console [${level}]: ${message}`, LogCategory.UI, 'main.ts');
  });

  mainWindow.on('closed', async () => {
    mainWindow = null;
    await logger.logI18n(LogLevel.INFO, 'logs.app.window.closed', 'Main window closed', LogCategory.APP, 'main.ts');
  });

  // 控制新窗口创建 - 安全措施
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    logger.info(`Attempting to open external URL: ${url}`, LogCategory.APP, 'main.ts').catch(console.error);
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // 阻止导航到外部URL
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);

    if (parsedUrl.origin !== new URL(RENDERER_URL).origin) {
      event.preventDefault();
      logger.warn(`Blocked navigation to external URL: ${navigationUrl}`, LogCategory.APP, 'main.ts').catch(console.error);
    }
  });
}

/**
 * 创建系统托盘
 */
async function createTray(): Promise<void> {
  try {
    const trayIcon = nativeImage.createFromPath(join(__dirname, '../../assets/icon.png'));
    tray = new Tray(trayIcon.resize({ width: 16, height: 16 }));

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示主窗口',
        click: () => {
          if (mainWindow) {
            mainWindow.show();
            mainWindow.focus();
          } else {
            createMainWindow().catch(console.error);
          }
        }
      },
      {
        label: '退出',
        click: () => {
          app.quit();
        }
      }
    ]);

    tray.setToolTip('QuickStart');
    tray.setContextMenu(contextMenu);

    // 双击托盘图标显示窗口
    tray.on('double-click', () => {
      if (mainWindow) {
        mainWindow.show();
        mainWindow.focus();
      }
    });

    await logger.logI18n(LogLevel.INFO, 'logs.app.tray.created', 'System tray created successfully', LogCategory.APP, 'main.ts');
  } catch (error) {
    await errorLogger.logError(error as Error, 'main.ts', LogCategory.APP);
  }
}

/**
 * 应用程序事件处理
 */
app.whenReady().then(async () => {
  perfLogger.start('appReady');

  try {
    // 初始化配置管理器（需要在i18n初始化之前）
    await configManager.initialize();
    await logger.logConfigLoadSuccess('main.ts', 'ConfigManager');

    // 读取配置文件中的语言设置
    let savedLanguage = 'zh-CN'; // 默认语言
    try {
      const i18nConfig = configManager.getConfig('i18n-config');
      if (i18nConfig?.currentLanguage) {
        savedLanguage = i18nConfig.currentLanguage;
        // 注意：此时i18n尚未初始化，使用英文日志是合理的
        console.log(`Loading saved language: ${savedLanguage}`);
      }
    } catch (error) {
      // 注意：此时i18n尚未初始化，使用英文日志是合理的
      console.warn('Failed to load saved language, using default:', error);
    }

    // 使用保存的语言初始化主进程i18n
    await mainI18n.initialize(savedLanguage as SupportedLanguage);

    // 设置i18n翻译函数到logger
    logger.setI18nFunction((key: string, fallback: string, params?: Record<string, unknown>) => {
      return mainI18n.t(key, fallback, params);
    });

    // 使用i18n记录应用就绪
    await logger.logI18n(LogLevel.INFO, 'logs.app.startup.ready', 'App is ready', LogCategory.APP, 'main.ts');

    // 环境信息详细日志 - 使用i18n
    await logger.logI18n(LogLevel.INFO, 'logs.app.environment.header', '=== Environment Configuration ===', LogCategory.APP, 'main.ts');
    await logger.logI18n(LogLevel.INFO, 'logs.app.environment.node_env', 'NODE_ENV: {{env}}', LogCategory.APP, 'main.ts', { env: process.env.NODE_ENV ?? 'undefined' });
    await logger.logI18n(LogLevel.INFO, 'logs.app.environment.is_packaged', 'app.isPackaged: {{packaged}}', LogCategory.APP, 'main.ts', { packaged: app.isPackaged });
    await logger.logI18n(LogLevel.INFO, 'logs.app.environment.is_dev', 'isDev (computed): {{dev}}', LogCategory.APP, 'main.ts', { dev: isDev });
    await logger.logI18n(LogLevel.INFO, 'logs.app.environment.platform', 'process.platform: {{platform}}', LogCategory.APP, 'main.ts', { platform: process.platform });
    await logger.logI18n(LogLevel.INFO, 'logs.app.environment.arch', 'process.arch: {{arch}}', LogCategory.APP, 'main.ts', { arch: process.arch });
    await logger.logI18n(LogLevel.INFO, 'logs.app.environment.renderer_url', 'RENDERER_URL: {{url}}', LogCategory.APP, 'main.ts', { url: RENDERER_URL });
    await logger.logI18n(LogLevel.INFO, 'logs.app.environment.dirname', '__dirname: {{dirname}}', LogCategory.APP, 'main.ts', { dirname: __dirname });
    await logger.logI18n(LogLevel.INFO, 'logs.app.environment.footer', '===================', LogCategory.APP, 'main.ts');
  } catch (error) {
    // i18n初始化失败时使用英文日志
    await logger.info('App is ready (i18n initialization failed)', LogCategory.APP, 'main.ts');
    console.error('Failed to initialize main process i18n:', error);
  }

  try {
    // 初始化应用目录
    initializeAppDirectories();

    // 初始化数据库管理器
    databaseManager.initialize();
    await logger.logI18n(LogLevel.INFO, 'logs.app.database.initialized', 'DatabaseManager initialized', LogCategory.DB, 'main.ts');

    // 初始化背景缓存管理器
    await backgroundCacheManager.initialize();
    await logger.logI18n(LogLevel.INFO, 'logs.app.cache.initialized', 'BackgroundCacheManager initialized', LogCategory.APP, 'main.ts');

    // 注册IPC处理器
    registerIPCHandlers();

    // 创建主窗口
    await createMainWindow();

    // 创建系统托盘
    await createTray();

    // macOS 特殊处理
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow().catch(console.error);
      }
    });

    await perfLogger.end('appReady', 'main.ts');
  } catch (error) {
    await errorLogger.logError(error as Error, 'main.ts', LogCategory.APP);
    app.quit();
  }
});

// 所有窗口关闭时的处理
app.on('window-all-closed', () => {
  // macOS 下保持应用运行
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 应用退出前的清理
app.on('before-quit', async () => {
  await logger.logI18n(LogLevel.INFO, 'logs.app.startup.shutdown', 'Application is shutting down', LogCategory.APP, 'main.ts');

  // 清理配置管理器
  configManager.destroy();

  // 清理日志管理器
  try {
    const logManager = logger.getLogManager();
    await logManager.destroy();
  } catch (error) {
    // 应用退出时的清理错误，使用英文是合理的
    console.error('Failed to cleanup logger:', error);
  }
});

// 安全相关：阻止新窗口创建
app.on('web-contents-created', (_, contents) => {
  contents.setWindowOpenHandler(({ url }: { url: string }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
});


