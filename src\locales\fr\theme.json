{"title": "Paramètres de thème", "presets": {"iosBlue": "iOS Bleu", "macosGreen": "macOS Vert", "appleOrange": "Apple Orange", "deepSpaceGray": "Gris espace profond", "midnightBlue": "Bleu minuit"}, "colors": {"title": "Couleurs d'interface", "primary": "Couleur principale", "secondary": "<PERSON>uleur secondaire", "background": "Couleur d'arrière-plan", "surface": "Couleur de surface", "text": "Couleur du texte", "textSecondary": "Couleur du texte secondaire", "border": "<PERSON><PERSON><PERSON> de bordure", "success": "<PERSON><PERSON><PERSON> de succès", "warning": "Couleur d'avertissement", "error": "<PERSON><PERSON><PERSON> d'erreur", "info": "Couleur d'information", "accent": "Couleur d'accent", "muted": "<PERSON><PERSON><PERSON> atténu<PERSON>", "highlight": "<PERSON><PERSON>ur de surbrillance", "shadow": "Couleur d'ombre", "interface": "Couleurs d'interface", "labels": {"surface": "Couleur de surface", "text": "Couleur du texte", "textSecondary": "Texte secondaire", "border": "<PERSON><PERSON><PERSON> de bordure", "shadow": "Couleur d'ombre"}, "descriptions": {"surface": "Couleur d'arrière-plan pour les cartes et panneaux", "text": "Couleur principale du texte", "textSecondary": "Couleur du texte secondaire et des descriptions", "border": "Couleur des bordures et séparateurs", "shadow": "Couleur des ombres et ombres portées"}}, "fonts": {"title": "Paramètres de police", "family": "Famille de police", "size": "Taille de police", "weight": "Épaisseur de police", "lineHeight": "<PERSON><PERSON> <PERSON> ligne", "letterSpacing": "Espacement des lettres", "systemFont": "Police système", "customFont": "Police personnalisée", "configuration": "Configuration de police", "options": {"appleSystem": "Police système Apple", "microsoftYaHei": "Microsoft YaHei", "pingFangSC": "PingFang SC", "helveticaNeue": "Helvetica Neue", "sfProDisplay": "SF Pro Display", "segoeUI": "Segoe UI"}, "weights": {"thin": "Fin", "light": "<PERSON><PERSON><PERSON>", "regular": "Normal", "medium": "<PERSON><PERSON><PERSON>", "semibold": "Semi-gras", "bold": "Gras", "heavy": "<PERSON><PERSON>"}}, "effects": {"title": "Effets visuels", "glassEffect": "<PERSON><PERSON><PERSON> de verre d<PERSON>", "transparency": "Transparence", "blur": "<PERSON><PERSON>", "shadows": "Effets d'ombre", "animations": "Effets d'animation", "transitions": "Effets de transition", "borderRadius": "Rayon de bordure", "gradient": "Effets de dégradé", "compactMode": "Mode compact"}, "background": {"title": "Paramètres d'arrière-plan", "type": "Type d'arrière-plan", "color": "Arrière-plan couleur unie", "gradient": "Arrière-plan dégradé", "image": "Arrière-plan image", "pattern": "Arrière-plan motif", "opacity": "Opacité de l'arrière-plan", "position": "Position de l'arrière-plan", "size": "Taille de l'arrière-plan", "repeat": "Répétition de l'arrière-plan", "attachment": "Attachement de l'arrière-plan", "blend": "Mode de fusion", "enabled": "Activer l'arrière-plan", "basicSettings": "Paramètres de base", "colorSettings": "Paramètres de couleur", "gradientSettings": "Paramètres de dégradé", "imageSettings": "Paramètres d'image", "reset": "Réinitialiser les paramètres", "gradientType": "Type de dégradé", "gradientDirection": "Direction du dégradé", "gradientColors": "Couleurs du dégradé", "startColor": "D<PERSON>but", "endColor": "Fin", "colorStop": "<PERSON><PERSON><PERSON>", "addColor": "<PERSON><PERSON><PERSON> couleur", "removeColor": "Supp<PERSON><PERSON> couleur", "noGradientColors": "<PERSON><PERSON><PERSON> couleur de d<PERSON>", "clickAddColor": "Cliquez sur \"Ajouter couleur\" pour ajouter des couleurs de dégradé", "gradientTypes": {"linear": "Dégradé linéaire", "radial": "Dégradé radial"}, "types": {"none": "Pas d'arrière-plan", "color": "Couleur unie", "gradient": "<PERSON><PERSON><PERSON><PERSON>", "image": "Image"}, "performanceSettings": "Paramètres de performance", "performanceTip": "Conseil de performance", "performanceDesc": "Les images d'arrière-plan haute résolution peuvent affecter les performances de l'application. Il est recommandé d'utiliser des paramètres de compression appropriés.", "localImage": "Image locale", "networkImage": "<PERSON> réseau", "dragOrClick": "Cliquez ou faites glisser l'image dans cette zone pour télécharger", "supportFormats": "Prend en charge les formats JPG, PNG, GIF, WebP, maximum 50MB", "compressionTip": "Conseil de compression", "compressionDesc": "Les grandes images seront automatiquement compressées pour optimiser les performances tout en maintenant de bons effets visuels.", "displayMode": "Mode d'affichage", "blur": "<PERSON><PERSON>", "brightness": "Luminosité", "contrast": "Contraste", "saturation": "Saturation", "modes": {"stretch": "<PERSON><PERSON><PERSON>", "tile": "Mosaïque", "center": "Centrer", "cover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contain": "Contenir"}, "processing": "Traitement de l'image...", "urlLoadSuccess": "Image réseau chargée avec succès", "urlLoadFailed": "Échec du chargement de l'image réseau", "removeSuccess": "Image d'arrière-plan supprimée", "preview": "<PERSON><PERSON><PERSON><PERSON>", "viewFullSize": "Voir en taille réelle", "removeImage": "Supprimer l'image", "loadError": "Échec du chargement de la configuration d'arrière-plan", "noConfig": "Configuration d'arrière-plan non disponible", "configWillBeCreated": "La configuration sera créée lors de la première utilisation", "urlImage": "<PERSON> réseau", "enterImageUrl": "Entrez l'URL de l'image (https://...)", "urlTip": "Conseil pour les images réseau", "urlDesc": "Veuillez vous assurer que le lien de l'image est accessible et prend en charge le protocole HTTPS.", "enterUrl": "Veuillez entrer le lien de l'image", "uploadSuccess": "Image d'arrière-plan téléchargée avec succès", "uploadFailed": "Échec du téléchargement de l'image d'arrière-plan", "downloadingImage": "Téléchargement de l'image réseau...", "processingLocalImage": "Traitement de l'image locale...", "compressingImage": "Compression de l'image pour optimiser les performances...", "savingImage": "Sauvegarde de l'image d'arrière-plan...", "imageSetSuccess": "Image d'arrière-plan définie avec succès", "imageSetFailed": "Échec de la définition de l'image d'arrière-plan", "usingMemoryMode": "Utilisation du mode mémoire pour définir l'arrière-plan (nécessite une réinitialisation après redémarrage)", "imageSetSuccessMemory": "Image d'arrière-plan définie avec succès (mode temporaire)", "imageSetFailedComplete": "La définition de l'image d'arrière-plan a complètement échoué", "defaultImageName": "Image d'arrière-plan", "duplicateFileName": "Nom de fichier en double", "duplicateFileContent": "Le nom de fichier \"{{fileName}}\" existe déj<PERSON>, renommer et sauvegarder ?", "renameAndSave": "Renommer et sauvegarder", "cancelSave": "Annuler la sauvegarde de l'image d'arrière-plan", "permissionDeniedError": "Permissions insuffisantes pour le répertoire de cache, veuillez vérifier les permissions de l'application", "diskSpaceError": "Espace disque insuffisant, impossible de sauvegarder l'image d'arrière-plan", "cacheNotInitializedError": "Système de cache non initialisé, nouvelle tentative...", "imageCacheFailedError": "Échec du cache d'image : {{message}}", "imageValidation": {"unsupportedFormat": "Format d'image non pris en charge : {{format}}. Formats pris en charge : JPEG, PNG, GIF, WebP", "fileTooLarge": "Fichier image trop volumineux : {{size}}MB. Maximum pris en charge : 50MB", "fileInvalid": "Objet fi<PERSON><PERSON> invalide", "emptyFile": "La taille du fichier est de 0, il s'agit peut-être d'un fichier vide", "invalidFileType": "Type de fichier invalide : {{type}}, type d'image attendu", "loadTimeout": "<PERSON><PERSON><PERSON> d'attente de chargement d'image (5 secondes), le fichier peut être corrompu ou trop volumineux", "readTimeout": "<PERSON><PERSON><PERSON> d'attente de lecture du fichier (10 secondes), le fichier peut être trop volumineux ou corrompu", "invalidDimensions": "Dimensions d'image invalides, le fichier peut être corrompu ou ne pas être un format d'image valide", "cannotReadFile": "Impossible de lire le fichier image", "jpegCorrupted": "Le fichier JPEG existe mais peut être corrompu, ve<PERSON><PERSON><PERSON> essayer de le sauvegarder à nouveau avec un logiciel d'édition d'images", "pngCorrupted": "Le fichier PNG existe mais peut être corrompu, ve<PERSON><PERSON><PERSON> essayer de le sauvegarder à nouveau avec un logiciel d'édition d'images", "gifCorrupted": "Le fichier GIF existe mais peut être corrompu, ve<PERSON><PERSON><PERSON> essayer de le sauvegarder à nouveau avec un logiciel d'édition d'images", "webpCorrupted": "Le fichier WebP existe mais peut être corrompu ou non pris en charge dans l'environnement actuel", "bmpCorrupted": "Le fichier BMP existe mais peut être corrompu, il est recommandé de le convertir au format JPG ou PNG", "webpNotSupported": "Le format WebP n'est pas pris en charge dans le navigateur actuel, veuillez utiliser le format JPG ou PNG", "fileCorruptedGeneric": "Le fichier n'est pas un format d'image valide ou est corrompu. En-tête du fichier : {{header}}...", "unsupportedFileType": "Type de fichier non pris en charge : {{type}}. Veuillez sélectionner un fichier image valide (JPG, PNG, GIF, WebP)", "fileTooLargeSimple": "Fichier image trop volumineux, veuil<PERSON>z sélectionner une image de moins de 50MB", "corruptionSuggestions": "Causes possibles : le fichier peut être corrompu ; le format d'image peut ne pas être pris en charge ; les permissions de fichier peuvent être problématiques"}, "imageProcessing": {"setImageSourceFailed": "Échec de la définition de la source d'image: {{message}}", "fileReadFailed": "Échec de la lecture du fichier: {{message}}", "unknownError": "<PERSON><PERSON><PERSON> inconnue"}, "imageNetwork": {"downloadTimeoutRetry": "<PERSON><PERSON><PERSON> d'attente de téléchargement d'image réseau, veuillez vérifier la connexion réseau ou réessayer plus tard", "corsAccessDenied": "Impossible d'accéder à cette image, le serveur n'autorise pas les requêtes cross-origin", "networkConnectionError": "Erreur de connexion réseau, veuillez vérifier les paramètres réseau", "serverError": "Erreur serveur: {{message}}", "fileTooLargeNetwork": "Fichier d'image réseau trop volumineux", "invalidImageLink": "Le lien n'est pas un fichier image valide", "genericError": "{{baseMessage}}: {{details}}"}, "cache": {"initializationFailed": "L'initialisation du gestionnaire de cache a échoué : {{message}}", "permissionDeniedCreate": "Permission refusée : Impossible de créer ou d'écrire dans le répertoire de cache : {{dir}}. Veuillez vérifier les permissions du dossier.", "insufficientDiskSpaceCreate": "Espace disque insuffisant pour créer le répertoire de cache : {{dir}}", "invalidPathCreate": "Chemin invalide : Un fichier existe là où le répertoire devrait être créé : {{dir}}", "ensureCacheDirectoryFailed": "Échec de l'assurance du répertoire de cache : {{message}}", "insufficientDiskSpaceCache": "Espace disque insuffisant : {{required}}MB requis, {{available}}MB disponible", "fileWriteVerificationFailed": "La vérification d'écriture du fichier a échoué : {{expected}} octets attendus, {{actual}} octets obtenus", "insufficientDiskSpace": "Espace disque insuffisant pour mettre en cache l'image", "permissionDeniedWrite": "Permission refusée : Impossible d'écrire dans le répertoire de cache : {{dir}}", "tooManyOpenFiles": "Trop de fichiers ouverts : Impossible de mettre en cache l'image", "cacheImageFailed": "Échec de la mise en cache de l'image : {{message}}", "cleanupPartialFileFailed": "Échec du nettoyage du fichier partiel"}, "cachedImages": "Images en cache", "noCachedImages": "Aucune image en cache", "cachedImagesDesc": "Cliquez sur l'image pour changer l'arrière-plan, cliquez sur le bouton supprimer pour retirer du cache", "switchToImage": "Basculer vers cet arrière-plan", "removeFromCache": "Supprimer du cache", "confirmRemoveCache": "Confirmer la suppression du cache", "confirmRemoveCacheContent": "Êtes-vous sûr de vouloir supprimer l'image \"{{fileName}}\" du cache ? Cette action ne peut pas être annulée.", "removeCacheSuccess": "Image supprimée du cache", "removeCacheFailed": "Échec de la suppression de l'image en cache", "imageCache": "<PERSON><PERSON> d'images", "cacheManagement": "Gestion du cache", "customFileName": "Nom de fichier personnal<PERSON>", "enterCustomFileName": "Veuillez saisir un nom de fichier personnalisé", "fileNameInvalid": "Nom de fichier invalide", "renameImage": "Renommer l'image", "newFileName": "Nouveau nom de fichier", "renameSuccess": "Renomma<PERSON> r<PERSON>", "renameFailed": "Échec du renommage", "enterNewFileName": "Veuillez saisir le nouveau nom de fichier"}, "actions": {"apply": "App<PERSON><PERSON> le thème", "reset": "Réinitialiser le thème", "customize": "Personnal<PERSON> le thème", "duplicate": "<PERSON><PERSON><PERSON><PERSON> le thème", "rename": "<PERSON>mmer le thème", "share": "Partager le thème"}, "messages": {"themeApplied": "Th<PERSON> appliqué", "themeReset": "Thème réinitialisé", "themeSaved": "Thème sauve<PERSON>", "themeLoaded": "Thème chargé", "themeExported": "Configuration du thème exportée", "themeImported": "Configuration du thème importée", "themeImportError": "Erreur de format du fichier de configuration du thème", "layoutUpdated": "Paramètres de mise en page mis à jour", "layoutUpdateFailed": "Échec de la mise à jour des paramètres de mise en page", "layoutUpdateError": "Erreur lors de la mise à jour des paramètres de mise en page", "loadingLayout": "Chargement des paramètres de mise en page...", "loadLayoutFailed": "Échec du chargement des paramètres de mise en page", "noLayoutConfig": "Impossible de charger les paramètres de mise en page", "checkConfigSystem": "Veuillez vérifier si le système de configuration fonctionne correctement", "invalidThemeFile": "<PERSON><PERSON><PERSON> de thème invalide", "themeNameRequired": "Veuillez saisir le nom du thème", "themeNameExists": "Le nom du thème existe déjà", "confirmDeleteTheme": "Confirmer la suppression de ce thème ?", "confirmResetTheme": "Confirmer la réinitialisation des paramètres du thème ?", "themeDeleteConfirm": "Confirmer la suppression de ce thème ?", "themeResetConfirm": "Confirmer la réinitialisation du thème par défaut ?", "customThemeNameRequired": "Le nom du thème personnalisé est requis", "customThemeNameExists": "Le nom du thème personnalisé existe déjà", "themeFileInvalid": "Format de fichier de thème invalide", "themeLoadError": "Erreur de chargement du thème", "themeSaveError": "<PERSON><PERSON><PERSON> de sauvegarde du thème", "previewMode": "Mode aperçu activé", "previewModeDisabled": "Mode aperçu désactivé", "colorPickerTitle": "Sélectionner la couleur", "resetToDefault": "Réinitialiser par défaut", "importTheme": "Importer le thème", "exportTheme": "Exporter le thème", "themePreview": "Aperçu du thème", "applyChanges": "Appliquer les modifications", "discardChanges": "Annuler les modifications", "unsavedChanges": "Vous avez des modifications de thème non sauvegardées"}, "layout": {"title": "Paramètres de mise en page", "sidebar": "Barre la<PERSON>", "sidebarWidth": "<PERSON>ur de la barre latérale", "sidebarPosition": "Position de la barre latérale"}, "global": {"title": "Thème global", "primaryColor": "Couleur principale", "primaryColorDesc": "Affecte la couleur des boutons, liens et autres éléments principaux", "mode": "Mode d'affichage", "darkMode": "Mode sombre", "modeDesc": "Basculer entre les modes d'affichage clair et sombre", "font": "Paramètres de police", "fontSize": "Taille de police", "borderRadius": "Rayon de bordure"}, "ui": {"title": "Paramètres de style", "description": "Personnaliser l'apparence et le thème de l'application", "interfaceColors": "Couleurs de l'interface", "themePresets": "Préréglages de thème", "typography": "Typographie", "visualEffects": "Effets visuels", "layoutSettings": "Paramètres de mise en page", "primaryColorDesc": "Utilisé pour les boutons, liens et autres éléments principaux", "colorLabels": {"background": "Arrière-plan", "surface": "Surface", "text": "Texte", "textSecondary": "Texte secondaire", "border": "Bordure", "shadow": "Ombre"}, "fontSize": "Taille de police", "interfaceEffects": "Effets d'interface", "sidebar": {"title": "Paramètres de la barre latérale", "position": "Position de la barre latérale", "width": "<PERSON>ur de la barre latérale", "collapsed": "Réduit par défaut", "autoHide": "Masquage automatique", "positions": {"left": "G<PERSON><PERSON>", "right": "<PERSON><PERSON><PERSON>"}}, "fileList": {"title": "Paramètres de la liste de fichiers", "showSize": "Afficher la taille", "showModifiedTime": "Afficher l'heure de modification", "showAddedTime": "Afficher l'heure d'ajout", "showLaunchCount": "Afficher le nombre de lancements"}, "statusBar": {"title": "Paramètres de la barre d'état", "visible": "Afficher la barre d'état", "showFileCount": "Afficher le nombre de fichiers", "showPath": "<PERSON><PERSON><PERSON><PERSON> le chemin"}}}